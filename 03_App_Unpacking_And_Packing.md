# Android 逆向工程：脱壳与加壳

## 1. 核心概念

### 1.1 什么是应用加壳 (App Packing)？

**加壳**，也称为**加固** (Reinforcement)，是一种保护 Android 应用不被轻易逆向分析的技术。其核心思想是将原始的 DEX 文件（Dalvik Executable）进行加密、隐藏或变形，然后用一个“外壳”程序（Shell）来包裹它。

当加壳后的应用启动时：
1.  首先运行的是这个“外壳”程序。
2.  “外壳”程序会在运行时（通常是在 `Application.onCreate()` 或更早的 attachBaseContext 阶段）在内存中解密并加载原始的 DEX 文件。
3.  最后，将程序的执行权交还给原始的 DEX 代码。

这个过程对用户是透明的，但对逆向分析者制造了巨大的障碍，因为直接反编译 APK 得到的将是“外壳”的代码，而不是真正的业务逻辑代码。

**加壳的主要目的**:
*   **防止反编译**: 使 Jadx、Apktool 等工具无法直接分析核心代码。
*   **防止静态分析**: 隐藏代码逻辑、敏感字符串和 API 调用。
*   **增加调试难度**: 集成反调试、反Hook等功能，干扰动态分析。
*   **防止重打包**: 校验应用签名和完整性，防止二次打包和盗版。

**主流加固厂商**: 腾讯乐固、360加固、爱加密、网易易盾等。

### 1.2 什么是应用脱壳 (App Unpacking)？

**脱壳**是与加壳相反的过程，指的是通过技术手段将被加密或隐藏的原始 DEX 文件从内存中提取（Dump）出来的过程。成功脱壳后，我们就可以得到应用真正的、未受保护的 DEX 文件，从而可以进行深入的静态和动态分析。

## 2. 加壳技术分类

1.  **DEX 加密（一代壳）**: 将整个 DEX 文件加密，运行时在内存中解密并加载。
2.  **代码抽取与指令替换（二代壳）**: 将一部分方法的代码体（CodeItem）抽取出来，隐藏在其他地方。当方法被调用时，再动态填充回来。或者将关键指令替换为自定义的解释器指令。
3.  **VMP (Virtual Machine Protection) 加固（三代壳）**: 将一部分 Java/Native 代码转换为自定义的虚拟机指令，运行时通过内置的解释器来执行。这是目前最强的加固技术之一，逆向难度极高。
4.  **SO 库加固**: 对 `lib/` 目录下的 so 文件进行加密和保护，防止被 IDA Pro 等工具直接分析。

## 3. 脱壳技术与工具

脱壳的核心思想是：**无论壳如何保护，代码最终都要在内存中解密并执行**。因此，脱壳的关键在于找到合适的时机和方法，从内存中将解密后的 DEX 文件 dump 下来。

### 3.1 脱壳方法

*   **内存 Dump**: 在 DEX 文件被完整加载到内存后，通过遍历进程的内存空间 (`/proc/self/maps`)，找到 DEX 文件的内存区域并将其完整地 dump 到本地文件。
*   **Hook 关键函数**: 通过 Hook Android 框架层加载 DEX 的关键函数（如 `DexFile.openDexFile` 或 `DexClassLoader` 的构造函数），在函数执行时获取到解密后的 DEX 数据。

### 3.2 常用脱壳工具

| 工具 | 主要用途 | 描述 |
| :--- | :--- | :--- |
| **Frida-based Scripts** | 通用脱壳 | 利用 Frida 的 Hook 能力，编写 JavaScript 脚本来监控 DEX 加载过程并自动 dump。这是目前最灵活、最主流的脱壳方式。例如，[frida-unpack](https://github.com/GuoQiang1993/frida-unpack) 项目。 |
| **BlackDex** | 全自动脱壳 (需 Root) | 一个在 Android 设备上运行的脱壳工具，它能通过修改系统环境，在应用加载 DEX 时进行拦截和 dump。支持高版本的 Android 系统，对很多一代壳有很好的效果。 |
| **Fdex2 / DexDump** | 基于 Xposed 的脱壳 | 经典的脱壳工具，通过 Xposed 框架 Hook 系统底层函数来实现脱壳。由于 Xposed 的局限性，对高版本 Android 和新加固方案的支持较差。 |
| **GDB / IDA Pro** | SO 库脱壳 | 用于对加固的 so 文件进行动态调试和内存 dump。 |

## 4. 通用脱壳思路

面对一个加固的应用，可以遵循以下思路：

1.  **查壳**: 使用 `Apktool` 或文件查看器检查 APK 结构，看是否存在主流加固厂商的特征（如特定的 so 文件名、assets 目录下的文件等），判断应用是否加固以及是哪家的壳。
2.  **尝试自动化工具**: 首先使用 BlackDex 或成熟的 Frida 脱壳脚本等自动化工具尝试脱壳。如果成功，则可以节省大量时间。
3.  **手动分析 (如果自动工具失败)**:
    *   **静态分析壳代码**: 使用 Jadx 分析外壳 DEX 的代码，重点关注 `Application` 类（特别是 `attachBaseContext` 和 `onCreate` 方法），寻找壳的初始化和解密逻辑。
    *   **动态调试**: 使用 Frida Hook 可疑的函数，打印日志、观察行为，定位到原始 DEX 被加载的关键点。
    *   **编写定制化脚本**: 针对该应用的加固逻辑，编写特定的 Frida 脚本来 Hook 关键点并 dump DEX。
4.  **DEX 修复**: dump 出来的 DEX 文件可能存在一些问题（如被抽取的 CodeItem 未填充），需要使用工具（如 [dexfixer](https://github.com/magic-transform/dexfixer)）进行修复，才能被 Jadx 等工具正常打开。

---

接下来，我们将探讨强大的 **Hook 技术**。
