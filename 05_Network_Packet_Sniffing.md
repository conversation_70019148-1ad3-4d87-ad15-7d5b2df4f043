# Android 逆向工程：网络抓包

## 1. 核心概念

### 1.1 什么是网络抓包 (Packet Sniffing)？

**抓包**是指捕获在网络接口上传输的数据包，并对其内容进行分析的过程。在 Android 逆向中，抓包特指拦截和分析目标应用与服务器之间的网络通信数据。

这是逆向分析中至关重要的一环，因为现代应用的大部分核心逻辑和数据都通过网络 API 与服务器交互。

### 1.2 抓包的主要目的

*   **分析通信协议**: 了解应用使用的是 HTTP/HTTPS、WebSocket 还是其他自定义协议。
*   **定位核心 API**: 找到负责登录、获取用户信息、支付等关键功能的 API 接口。
*   **分析请求/响应数据**: 查看 API 请求的参数、Header、Cookie，以及服务器返回的数据格式（通常是 JSON 或 XML），从而理解应用的数据交互模式。
*   **定位加密参数**: 发现请求中哪些参数是经过加密的（如 `sign`, `token`），为后续的算法分析提供目标。
*   **重放攻击/修改数据**: 拦截请求，修改其参数后重新发送（重放），测试服务器的逻辑漏洞。或者修改服务器的响应，绕过客户端的某些限制。

## 2. 抓包原理：中间人攻击 (MITM)

对于未加密的 HTTP 请求，抓包非常简单。但如今绝大部分应用都使用 **HTTPS** (HTTP over SSL/TLS) 进行加密通信，直接抓取的数据包是加密的，无法阅读。

为了抓取 HTTPS 的内容，我们必须采用**中间人攻击 (Man-in-the-Middle, MITM)** 的方法。

**MITM 流程**:
1.  **设置代理**: 在 Android 设备上设置一个 HTTP 代理，将所有网络流量指向我们 PC 上运行的抓包工具（如 Charles）。
2.  **伪造证书**: 当手机应用尝试与服务器建立 HTTPS 连接时，请求实际上被发送到了抓包工具。抓包工具会伪装成服务器，向应用返回一张**自己伪造的**服务器证书。
3.  **建立连接**: 应用信任并接受了这张伪造的证书后，会与抓包工具建立一个 HTTPS 连接。同时，抓包工具会再与真实的服务器建立另一个 HTTPS 连接。
4.  **解密与转发**: 此时，抓包工具作为中间人，可以解密应用发来的所有数据，展示给我们看，然后再用自己的连接将数据加密发送给真实服务器。反之亦然。

![MITM Workflow](https://i.imgur.com/JdYqj7E.png)

为了让第 3 步成功，必须在 Android 系统中**安装并信任**抓包工具的根证书 (CA Certificate)。

## 3. 常用抓包工具

| 工具 | 主要用途 | 描述 |
| :--- | :--- | :--- |
| **Charles** | HTTP/HTTPS 抓包 | 界面友好，功能强大，是移动端抓包的首选工具之一。支持 Mac, Windows, Linux。 |
| **Burp Suite** | Web 安全渗透测试 | 功能比 Charles 更强大，是专业的 Web 安全测试套件，抓包只是其功能之一。常用于漏洞挖掘。 |
| **Wireshark** | 底层网络协议分析 | 非常强大的网络协议分析器，能捕获所有类型的流量（TCP, UDP 等），但它工作在更底层，不专门用于解密 HTTPS，通常用于分析非 HTTP 协议。 |
| **tcpdump** | 命令行抓包 | 一个强大的命令行工具，可以直接在 Android 设备上运行（需要 root），用于捕获原始的网络数据包。 |

### 3.1 使用 Charles 进行抓包

1.  **PC 端安装 Charles**: 从官网下载并安装 Charles。
2.  **设置代理**: 启动 Charles，在菜单栏 `Proxy -> Proxy Settings` 中设置一个端口（如 `8888`）。
3.  **获取 PC 的 IP 地址**: 在终端或命令行中用 `ifconfig` (macOS/Linux) 或 `ipconfig` (Windows) 查看 PC 的局域网 IP 地址。
4.  **手机端设置代理**: 确保手机和 PC 在同一个 Wi-Fi 网络下。进入手机的 Wi-Fi 设置，修改网络，选择手动代理，服务器地址填入 PC 的 IP，端口填入 `8888`。
5.  **安装证书**: 在手机浏览器中访问 `chls.pro/ssl`，下载并安装 Charles 的根证书。**注意**: 从 Android 7.0 开始，应用默认不再信任用户安装的根证书。解决方法见下一节。
6.  **开始抓包**: 完成设置后，手机上的所有 HTTP/HTTPS 流量都会显示在 Charles 的界面中。

## 4. 应对抓包挑战：SSL Pinning

### 4.1 什么是 SSL Pinning？

**SSL Pinning**（证书锁定）是一种客户端应用对抗中间人攻击的安全机制。开启了 SSL Pinning 的应用，会在代码中内置一份或多份合法的服务器证书信息。在建立 HTTPS 连接时，应用会严格校验服务器返回的证书是否与内置的证书信息匹配。

由于抓包工具返回的是伪造的证书，校验必然会失败，导致应用直接断开网络连接。此时，你会发现即使正确安装了 Charles 的证书，也无法抓到该应用的包。

### 4.2 如何绕过 SSL Pinning？

绕过 SSL Pinning 的核心思想是 **Hook** 应用中执行证书校验的代码，使其失效。

**常用绕过工具和方法**:

*   **Objection**: 一个基于 Frida 的移动安全评估框架。它内置了绕过 SSL Pinning 的功能，通常一条命令即可搞定。
    ```bash
    # -g 表示对 Gated (有保护的) 应用进行操作
    objection -g com.example.app explore -s 'android sslpinning disable'
    ```
*   **Frida 脚本**: 在网上可以找到大量现成的、用于绕过 SSL Pinning 的 Frida 脚本。这些脚本会 Hook 常见的实现证书锁定的库（如 OkHttp, SSLCertificateSocketFactory）中的关键方法，并替换其逻辑。
    ```bash
    frida -U -f com.example.app -l ssl_unpinning_script.js
    ```
*   **修改源码/Smali**: 对于没有加固的应用，可以反编译后，在 Smali 代码中搜索 `checkServerTrusted` 等关键字，找到校验逻辑并将其注释或修改，然后重打包。
*   **修改 `network_security_config.xml`**: 对于 Android 7.0 以上的应用，如果它使用了网络安全配置（`network_security_config.xml`），可以尝试修改该文件，添加对用户自定义证书的信任。这同样需要重打包。
