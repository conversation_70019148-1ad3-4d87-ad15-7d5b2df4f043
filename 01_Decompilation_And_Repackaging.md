# Android 逆向工程：反编译与重打包

## 1. 核心概念

### 1.1 什么是反编译 (Decompilation)？

反编译是将已编译的、机器可读的代码（例如 Android 的 `.dex` 文件）转换回更易于人类理解的源代码或接近源代码的表示形式（如 Smali 代码或 Java 代码）的过程。这是逆向工程的基础，目的是为了理解应用的工作原理、分析其逻辑或寻找潜在的漏洞。

*   **APK 文件结构**: 一个 APK (`.apk`) 文件本质上是一个 ZIP 压缩包，其中包含了应用的所有资源和代码。
    *   `classes.dex`: Dalvik 虚拟机或 ART 环境执行的核心代码。
    *   `resources.arsc`: 编译后的资源文件，如字符串、布局引用等。
    *   `res/`: 未编译的资源目录。
    *   `lib/`: 存放 so 动态链接库的目录。
    *   `assets/`: 存放原生资源文件的目录。
    *   `AndroidManifest.xml`: 应用的清单文件，定义了组件、权限等。
    *   `META-INF/`: 存放签名和证书信息的目录。

### 1.2 什么是重打包 (Repackaging)？

重打包是在对反编译出的代码或资源进行修改后，重新将它们打包成一个可安装的 APK 文件的过程。为了使修改后的 APK 能够成功安装在设备上，必须对其进行**签名**。

## 2. 常用工具

| 工具 | 主要用途 | 描述 |
| :--- | :--- | :--- |
| **Apktool** | 反编译/回编译资源和 Smali | 最核心的工具。能将 `classes.dex` 反编译为 Smali 代码，并将 `resources.arsc`、`AndroidManifest.xml` 等二进制资源解码为可读的 XML 文件。修改后可以再用它打包回去。 |
| **Jadx / JADX-GUI** | 查看 Java 源码 | 非常强大的反编译器，可以直接将 `.dex` 或 `.apk` 文件转换为高质量的 Java 代码，并提供图形化界面方便代码阅读、搜索和分析。但它主要用于分析，不能直接用于重打包。 |
| **dex2jar** | Dex 转 Jar | 将 `classes.dex` 文件转换为 Java 的 `.jar` 文件。 |
| **JD-GUI** | 查看 Jar 文件 | 与 `dex2jar` 配合使用，用于查看转换后的 `.jar` 文件中的 Java 源代码。 |
| **Keytool** | 生成签名证书 | Java 开发工具包（JDK）自带的工具，用于创建自签名证书，以便为重打包后的 APK 签名。 |
| **Jarsigner** | APK 签名 | JDK 自带的工具，用于使用 `keytool` 生成的证书为 APK 文件签名。 |

## 3. 基本流程

一个典型的反编译、修改、重打包流程如下：

![Decompilation and Repackaging Workflow](https://i.imgur.com/wH8a2gE.png)

1.  **反编译 (Decompile)**: 使用 `Apktool` 将目标 APK 文件反编译成一个包含 Smali 代码和可读资源文件的目录。
    ```bash
    apktool d your_app.apk -o output_directory
    ```
2.  **分析与修改 (Analyze & Modify)**:
    *   使用 `Jadx-GUI` 打开原始 APK，分析其 Java 源码，定位到你想要修改的逻辑。
    *   在 `Apktool` 生成的 `output_directory` 目录中，找到对应的 Smali 文件或资源文件进行修改。例如，修改一个弹窗的文本内容，或者在 Smali 代码中禁用一个函数调用。
3.  **重打包 (Repackage)**: 使用 `Apktool` 将修改后的目录重新打包成一个新的 APK 文件。
    ```bash
    apktool b modified_directory -o unsigned_app.apk
    ```
4.  **签名 (Sign)**:
    *   **生成一个签名密钥**（如果还没有的话）：
        ```bash
        keytool -genkey -v -keystore my-release-key.keystore -alias my_alias -keyalg RSA -keysize 2048 -validity 10000
        ```
    *   **使用 `jarsigner` 为 APK 签名**：
        ```bash
        jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore unsigned_app.apk my_alias
        ```
5.  **安装与测试 (Install & Test)**: 将签名后的 APK 安装到 Android 设备或模拟器上进行测试。
    ```bash
    adb install signed_app.apk
    ```

---

接下来，我们将深入探讨**静态分析与动态调试**。
